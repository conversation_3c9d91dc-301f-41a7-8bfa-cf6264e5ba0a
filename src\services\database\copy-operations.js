import { createProjectItem } from "./lighting-operations.js";
import { createUnitItem } from "./unit-operations.js";
import { createScheduleItem } from "./schedule-operations.js";
import { createSceneItemBatch } from "./scene-operations.js";
import { updateMultiSceneScenes } from "./multi-scene-operations.js";
import { updateSequenceMultiScenes } from "./sequence-operations.js";

/**
 * Copy operations for project duplication
 */
export function copyProjectItems(db, originalItems, newProjectId) {
  try {
    const transaction = db.transaction(() => {
      const itemMappings = {
        lighting: new Map(),
        aircon: new Map(),
        unit: new Map(),
        curtain: new Map(),
        knx: new Map(),
        scene: new Map(),
        multi_scenes: new Map(),
        sequences: new Map(),
      };

      // Copy lighting items first (needed for curtain references)
      originalItems.lighting.forEach((item) => {
        const newItem = createProjectItem(db, newProjectId, {
          name: item.name,
          address: item.address,
          description: item.description,
          object_type: item.object_type,
        }, "lighting");
        itemMappings.lighting.set(item.id, newItem.id);
      });

      // Copy aircon items
      originalItems.aircon.forEach((item) => {
        const newItem = createProjectItem(db, newProjectId, {
          name: item.name,
          address: item.address,
          description: item.description,
          label: item.label,
        }, "aircon");
        itemMappings.aircon.set(item.id, newItem.id);
      });

      // Copy unit items
      originalItems.unit.forEach((item) => {
        const newItem = createUnitItem(db, newProjectId, {
          type: item.type,
          serial_no: item.serial_no,
          ip_address: item.ip_address,
          id_can: item.id_can,
          mode: item.mode,
          firmware_version: item.firmware_version,
          hardware_version: item.hardware_version,
          manufacture_date: item.manufacture_date,
          can_load: item.can_load,
          recovery_mode: item.recovery_mode,
          description: item.description,
          discovered_at: item.discovered_at,
          rs485_config: item.rs485_config,
          input_configs: item.input_configs,
          output_configs: item.output_configs,
        });
        itemMappings.unit.set(item.id, newItem.id);
      });

      // Copy curtain items (with lighting references)
      originalItems.curtain.forEach((item) => {
        const newItem = createProjectItem(db, newProjectId, {
          name: item.name,
          address: item.address,
          description: item.description,
          object_type: item.object_type,
          curtain_type: item.curtain_type,
          curtain_value: item.curtain_value,
          open_group_id: item.open_group_id ? itemMappings.lighting.get(item.open_group_id) : null,
          close_group_id: item.close_group_id ? itemMappings.lighting.get(item.close_group_id) : null,
          stop_group_id: item.stop_group_id ? itemMappings.lighting.get(item.stop_group_id) : null,
          pause_period: item.pause_period,
          transition_period: item.transition_period,
        }, "curtain");
        itemMappings.curtain.set(item.id, newItem.id);
      });

      // Copy KNX items (with lighting references)
      originalItems.knx.forEach((item) => {
        const newItem = createProjectItem(db, newProjectId, {
          name: item.name,
          address: item.address,
          type: item.type,
          factor: item.factor,
          feedback: item.feedback,
          rcu_group_id: item.rcu_group_id ? itemMappings.lighting.get(item.rcu_group_id) : null,
          knx_switch_group: item.knx_switch_group,
          knx_dimming_group: item.knx_dimming_group,
          knx_value_group: item.knx_value_group,
          description: item.description,
        }, "knx");
        itemMappings.knx.set(item.id, newItem.id);
      });

      // Copy scene items
      originalItems.scene.forEach((item) => {
        const newItem = createProjectItem(db, newProjectId, {
          name: item.name,
          address: item.address,
          description: item.description,
        }, "scene");
        itemMappings.scene.set(item.id, newItem.id);

        // Copy scene items with updated references
        const sceneItems = db.prepare("SELECT * FROM scene_items WHERE scene_id = ?").all(item.id);
        if (sceneItems.length > 0) {
          const newSceneItems = sceneItems.map((sceneItem) => {
            let newItemId = sceneItem.item_id;
            
            // Update item_id based on item_type
            if (sceneItem.item_type === "lighting" && itemMappings.lighting.has(sceneItem.item_id)) {
              newItemId = itemMappings.lighting.get(sceneItem.item_id);
            } else if (sceneItem.item_type === "aircon" && itemMappings.aircon.has(sceneItem.item_id)) {
              newItemId = itemMappings.aircon.get(sceneItem.item_id);
            } else if (sceneItem.item_type === "curtain" && itemMappings.curtain.has(sceneItem.item_id)) {
              newItemId = itemMappings.curtain.get(sceneItem.item_id);
            }

            return {
              item_type: sceneItem.item_type,
              item_id: newItemId,
              item_address: sceneItem.item_address,
              item_value: sceneItem.item_value,
              command: sceneItem.command,
              object_type: sceneItem.object_type,
            };
          });

          createSceneItemBatch(db, newItem.id, newSceneItems);
        }
      });

      // Copy multi-scene items
      originalItems.multi_scenes.forEach((item) => {
        const newItem = createProjectItem(db, newProjectId, {
          name: item.name,
          address: item.address,
          type: item.type,
          description: item.description,
        }, "multi_scenes");
        itemMappings.multi_scenes.set(item.id, newItem.id);

        // Copy multi-scene scenes with updated references
        const multiSceneScenes = db.prepare(`
          SELECT scene_id FROM multi_scene_scenes 
          WHERE multi_scene_id = ? 
          ORDER BY scene_order ASC
        `).all(item.id);

        if (multiSceneScenes.length > 0) {
          const newSceneIds = multiSceneScenes
            .map(mss => itemMappings.scene.get(mss.scene_id))
            .filter(id => id !== undefined);

          if (newSceneIds.length > 0) {
            updateMultiSceneScenes(db, newItem.id, newSceneIds);
          }
        }
      });

      // Copy sequence items
      originalItems.sequences.forEach((item) => {
        const newItem = createProjectItem(db, newProjectId, {
          name: item.name,
          address: item.address,
          description: item.description,
        }, "sequences");
        itemMappings.sequences.set(item.id, newItem.id);

        // Copy sequence multi-scenes with updated references
        const sequenceMultiScenes = db.prepare(`
          SELECT multi_scene_id FROM sequence_multi_scenes 
          WHERE sequence_id = ? 
          ORDER BY multi_scene_order ASC
        `).all(item.id);

        if (sequenceMultiScenes.length > 0) {
          const newMultiSceneIds = sequenceMultiScenes
            .map(sms => itemMappings.multi_scenes.get(sms.multi_scene_id))
            .filter(id => id !== undefined);

          if (newMultiSceneIds.length > 0) {
            updateSequenceMultiScenes(db, newItem.id, newMultiSceneIds);
          }
        }
      });

      // Copy schedule items
      originalItems.schedule.forEach((item) => {
        // Get associated scenes
        const scheduleScenes = db.prepare(`
          SELECT scene_id FROM schedule_scenes WHERE schedule_id = ?
        `).all(item.id);

        const newSceneIds = scheduleScenes
          .map(ss => itemMappings.scene.get(ss.scene_id))
          .filter(id => id !== undefined);

        const newItem = createScheduleItem(db, newProjectId, {
          name: item.name,
          description: item.description,
          time: item.time,
          days: JSON.parse(item.days),
          enabled: item.enabled,
          scenes: newSceneIds,
        });
      });

      return itemMappings;
    });

    return transaction();
  } catch (error) {
    console.error("Failed to copy project items:", error);
    throw error;
  }
}
