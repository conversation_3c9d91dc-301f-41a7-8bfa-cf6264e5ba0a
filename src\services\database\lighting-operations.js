import { getProjectItems, getProjectItemById, deleteProjectItem, duplicateProjectItem } from "./generic-operations.js";
import { getObjectValue } from "./utils.js";
import { updateProjectItem } from "./update-operations.js";

/**
 * Lighting operations
 */
export function getLightingItems(db, projectId) {
  return getProjectItems(db, projectId, "lighting");
}

export function createLightingItem(db, projectId, itemData) {
  return createProjectItem(db, projectId, itemData, "lighting");
}

export function updateLightingItem(db, id, itemData) {
  return updateProjectItem(db, id, itemData, "lighting");
}

export function deleteLightingItem(db, id) {
  return deleteProjectItem(db, id, "lighting");
}

export function duplicateLightingItem(db, id) {
  return duplicateProjectItem(db, id, "lighting", createProjectItem, getProjectItemById);
}

// Generic create project item function (used by multiple operations)
export function createProjectItem(db, projectId, itemData, tableName) {
  try {
    const {
      name,
      address,
      description,
      object_type,
      label,
      curtain_type,
      curtain_value,
      open_group_id,
      close_group_id,
      stop_group_id,
      pause_period,
      transition_period,
      type,
      factor,
      feedback,
      rcu_group_id,
      knx_switch_group,
      knx_dimming_group,
      knx_value_group,
    } = itemData;

    // Special validation for lighting to prevent duplicate addresses
    if (tableName === "lighting" && address) {
      const existingItems = db
        .prepare(
          "SELECT COUNT(*) as count FROM lighting WHERE project_id = ? AND address = ?"
        )
        .get(projectId, address);
      if (existingItems.count > 0) {
        throw new Error(`Address ${address} already exists.`);
      }
    }

    // Special validation for KNX
    if (tableName === "knx") {
      // Validate address range
      if (address < 0 || address > 511) {
        throw new Error("KNX address must be between 0 and 511.");
      }

      // Check for duplicate addresses
      const existingItems = db
        .prepare(
          "SELECT COUNT(*) as count FROM knx WHERE project_id = ? AND address = ?"
        )
        .get(projectId, address);
      if (existingItems.count > 0) {
        throw new Error(`KNX address ${address} already exists.`);
      }

      // Validate factor
      if (itemData.factor && itemData.factor < 1) {
        throw new Error("Factor must be greater than or equal to 1.");
      }
    }

    // Note: For aircon, we skip duplicate address validation here because
    // createAirconCard handles this validation at the card level before creating multiple items
    // with the same address but different object_types

    // Special validation for curtain to prevent duplicate addresses
    if (tableName === "curtain" && address) {
      const existingItems = db
        .prepare(
          "SELECT COUNT(*) as count FROM curtain WHERE project_id = ? AND address = ?"
        )
        .get(projectId, address);
      if (existingItems.count > 0) {
        throw new Error(`Address ${address} already exists.`);
      }
    }

    // Special validation for scene - address is now required
    if (tableName === "scene") {
      if (!address || !address.trim()) {
        throw new Error("Address is required for scene.");
      }
      if (!name || name.length > 15) {
        throw new Error(
          "Scene name is required and must be 15 characters or less."
        );
      }

      // Check maximum scene limit (100 scenes)
      const sceneCount = db
        .prepare("SELECT COUNT(*) as count FROM scene WHERE project_id = ?")
        .get(projectId);
      if (sceneCount.count >= 100) {
        throw new Error("Maximum 100 scenes allowed per project.");
      }
    }

    // Special validation for multi_scenes
    if (tableName === "multi_scenes") {
      if (!name || !name.trim()) {
        throw new Error("Name is required for multi-scene.");
      }

      if (name.length > 15) {
        throw new Error("Multi-scene name must be 15 characters or less.");
      }

      if (!address || !address.trim()) {
        throw new Error("Address is required for multi-scene.");
      }

      // Check maximum multi-scene limit (40 multi-scenes)
      const multiSceneCount = db
        .prepare(
          "SELECT COUNT(*) as count FROM multi_scenes WHERE project_id = ?"
        )
        .get(projectId);
      if (multiSceneCount.count >= 40) {
        throw new Error("Maximum 40 multi-scenes allowed per project.");
      }
    }

    // Special validation for sequences
    if (tableName === "sequences") {
      if (!name || !name.trim()) {
        throw new Error("Name is required for sequence.");
      }

      if (name.length > 15) {
        throw new Error("Sequence name must be 15 characters or less.");
      }

      if (!address || !address.trim()) {
        throw new Error("Address is required for sequence.");
      }

      // Check maximum sequence limit (20 sequences)
      const sequenceCount = db
        .prepare(
          "SELECT COUNT(*) as count FROM sequences WHERE project_id = ?"
        )
        .get(projectId);
      if (sequenceCount.count >= 20) {
        throw new Error("Maximum 20 sequences allowed per project.");
      }
    }

    // For aircon table, include label column
    if (tableName === "aircon") {
      const stmt = db.prepare(`
        INSERT INTO ${tableName} (project_id, name, address, description, label)
        VALUES (?, ?, ?, ?, ?)
      `);
      const result = stmt.run(projectId, name, address, description, label);
      return getProjectItemById(db, result.lastInsertRowid, tableName);
    } else if (tableName === "curtain") {
      // For curtain table, include curtain-specific fields
      // Address is required for curtain items
      if (!address) {
        throw new Error("Address is required for curtain items.");
      }
      const object_value = getObjectValue(object_type);

      const stmt = db.prepare(`
        INSERT INTO ${tableName} (project_id, name, address, description, object_type, object_value, curtain_type, curtain_value, open_group_id, close_group_id, stop_group_id, pause_period, transition_period)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        projectId,
        name,
        address,
        description,
        object_type,
        object_value,
        curtain_type,
        curtain_value || 3, // Default to CURTAIN_PULSE_2P value
        open_group_id || null,
        close_group_id || null,
        stop_group_id || null,
        pause_period || 0,
        transition_period || 0
      );
      return getProjectItemById(db, result.lastInsertRowid, tableName);
    } else if (tableName === "knx") {
      // For knx table, use new KNX-specific fields
      const stmt = db.prepare(`
        INSERT INTO ${tableName} (project_id, name, address, type, factor, feedback, rcu_group_id, knx_switch_group, knx_dimming_group, knx_value_group, description)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        projectId,
        name,
        address,
        type || 0,
        factor || 2,
        feedback || 0,
        rcu_group_id || null,
        knx_switch_group || null,
        knx_dimming_group || null,
        knx_value_group || null,
        description
      );
      return getProjectItemById(db, result.lastInsertRowid, tableName);
    } else if (tableName === "scene") {
      // For scene table, don't use object_type and object_value
      const stmt = db.prepare(`
        INSERT INTO ${tableName} (project_id, name, address, description)
        VALUES (?, ?, ?, ?)
      `);
      const result = stmt.run(projectId, name, address, description);
      return getProjectItemById(db, result.lastInsertRowid, tableName);
    } else if (tableName === "multi_scenes") {
      // For multi_scenes table, use type and address fields
      const { type, address } = itemData;
      const stmt = db.prepare(`
        INSERT INTO ${tableName} (project_id, name, address, type, description)
        VALUES (?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        projectId,
        name,
        address,
        type || 0,
        description
      );
      return getProjectItemById(db, result.lastInsertRowid, tableName);
    } else if (tableName === "sequences") {
      // For sequences table, use address field
      const { address } = itemData;
      const stmt = db.prepare(`
        INSERT INTO ${tableName} (project_id, name, address, description)
        VALUES (?, ?, ?, ?)
      `);
      const result = stmt.run(
        projectId,
        name,
        address,
        description
      );
      return getProjectItemById(db, result.lastInsertRowid, tableName);
    } else {
      // For other tables, use original structure with object_type and object_value
      const object_value = getObjectValue(object_type);
      const stmt = db.prepare(`
        INSERT INTO ${tableName} (project_id, name, address, description, object_type, object_value)
        VALUES (?, ?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        projectId,
        name,
        address,
        description,
        object_type,
        object_value
      );
      return getProjectItemById(db, result.lastInsertRowid, tableName);
    }
  } catch (error) {
    console.error(`Failed to create ${tableName} item:`, error);
    throw error;
  }
}
