/**
 * Database table schemas
 */
export function createTables(db) {
  const createProjectsTable = `
    CREATE TABLE IF NOT EXISTS projects (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `;

  const createLightingTable = `
    CREATE TABLE IF NOT EXISTS lighting (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      name TEXT,
      address TEXT NOT NULL,
      description TEXT,
      object_type TEXT DEFAULT 'OBJ_LIGHTING',
      object_value INTEGER DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
    )
  `;

  const createAirconItemsTable = `
    CREATE TABLE IF NOT EXISTS aircon (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      name TEXT,
      address TEXT NOT NULL,
      description TEXT,
      label TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
    )
  `;

  const createUnitTable = `
    CREATE TABLE IF NOT EXISTS unit (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      type TEXT,
      serial_no TEXT,
      ip_address TEXT,
      id_can TEXT,
      mode TEXT,
      firmware_version TEXT,
      hardware_version TEXT,
      manufacture_date TEXT,
      can_load BOOLEAN DEFAULT 0,
      recovery_mode BOOLEAN DEFAULT 0,
      description TEXT,
      discovered_at DATETIME,
      rs485_config TEXT, -- JSON string for RS485 configuration
      input_configs TEXT, -- JSON string for ALL input configurations
      output_configs TEXT, -- JSON string for ALL output configurations
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
    )
  `;

  const createCurtainTable = `
    CREATE TABLE IF NOT EXISTS curtain (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      name TEXT,
      address TEXT NOT NULL,
      description TEXT,
      object_type TEXT DEFAULT 'OBJ_CURTAIN',
      object_value INTEGER DEFAULT 2,
      curtain_type TEXT DEFAULT '',
      curtain_value INTEGER DEFAULT 0,
      open_group_id INTEGER,
      close_group_id INTEGER,
      stop_group_id INTEGER,
      pause_period INTEGER DEFAULT 0,
      transition_period INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
      FOREIGN KEY (open_group_id) REFERENCES lighting (id) ON DELETE SET NULL,
      FOREIGN KEY (close_group_id) REFERENCES lighting (id) ON DELETE SET NULL,
      FOREIGN KEY (stop_group_id) REFERENCES lighting (id) ON DELETE SET NULL
    )
  `;

  const createKnxTable = `
    CREATE TABLE IF NOT EXISTS knx (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      name TEXT,
      address INTEGER NOT NULL CHECK(address >= 0 AND address <= 511),
      type INTEGER NOT NULL DEFAULT 0,
      factor INTEGER NOT NULL DEFAULT 1 CHECK(factor >= 1),
      feedback INTEGER NOT NULL DEFAULT 0,
      rcu_group_id INTEGER,
      knx_switch_group TEXT,
      knx_dimming_group TEXT,
      knx_value_group TEXT,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
      FOREIGN KEY (rcu_group_id) REFERENCES lighting (id) ON DELETE SET NULL,
      UNIQUE(project_id, address)
    )
  `;

  const createSceneTable = `
    CREATE TABLE IF NOT EXISTS scene (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      name TEXT NOT NULL CHECK(length(name) <= 15),
      address TEXT NOT NULL,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
    )
  `;

  const createSceneItemsTable = `
    CREATE TABLE IF NOT EXISTS scene_items (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      scene_id INTEGER NOT NULL,
      item_type TEXT NOT NULL,
      item_id INTEGER NOT NULL,
      item_address TEXT,
      item_value TEXT,
      command TEXT,
      object_type TEXT,
      object_value INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (scene_id) REFERENCES scene (id) ON DELETE CASCADE
    )
  `;

  const createSceneAddressItemsTable = `
    CREATE TABLE IF NOT EXISTS scene_address_items (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      address TEXT NOT NULL,
      item_type TEXT NOT NULL,
      item_id INTEGER NOT NULL,
      object_type TEXT,
      object_value INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
      UNIQUE(project_id, address, item_type, item_id, object_type)
    )
  `;

  const createScheduleTable = `
    CREATE TABLE IF NOT EXISTS schedule (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      name TEXT,
      description TEXT,
      time TEXT NOT NULL,
      days TEXT NOT NULL,
      enabled BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
    )
  `;

  const createScheduleScenesTable = `
    CREATE TABLE IF NOT EXISTS schedule_scenes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      schedule_id INTEGER NOT NULL,
      scene_id INTEGER NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (schedule_id) REFERENCES schedule (id) ON DELETE CASCADE,
      FOREIGN KEY (scene_id) REFERENCES scene (id) ON DELETE CASCADE
    )
  `;

  const createMultiScenesTable = `
    CREATE TABLE IF NOT EXISTS multi_scenes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      name TEXT NOT NULL CHECK(length(name) <= 15),
      address TEXT NOT NULL,
      type INTEGER NOT NULL DEFAULT 0,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
    )
  `;

  const createMultiSceneScenesTable = `
    CREATE TABLE IF NOT EXISTS multi_scene_scenes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      multi_scene_id INTEGER NOT NULL,
      scene_id INTEGER NOT NULL,
      scene_order INTEGER NOT NULL DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (multi_scene_id) REFERENCES multi_scenes (id) ON DELETE CASCADE,
      FOREIGN KEY (scene_id) REFERENCES scene (id) ON DELETE CASCADE
    )
  `;

  const createSequencesTable = `
    CREATE TABLE IF NOT EXISTS sequences (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      project_id INTEGER NOT NULL,
      name TEXT NOT NULL CHECK(length(name) <= 15),
      address TEXT NOT NULL,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
    )
  `;

  const createSequenceMultiScenesTable = `
    CREATE TABLE IF NOT EXISTS sequence_multi_scenes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      sequence_id INTEGER NOT NULL,
      multi_scene_id INTEGER NOT NULL,
      multi_scene_order INTEGER NOT NULL DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (sequence_id) REFERENCES sequences (id) ON DELETE CASCADE,
      FOREIGN KEY (multi_scene_id) REFERENCES multi_scenes (id) ON DELETE CASCADE
    )
  `;

  // Note: unit_output_configs and unit_input_configs tables are removed
  // All I/O configuration data is now stored in unit.input_configs and unit.output_configs JSON columns

  try {
    db.exec(createProjectsTable);
    db.exec(createLightingTable);
    db.exec(createAirconItemsTable);
    db.exec(createUnitTable);
    db.exec(createCurtainTable);
    db.exec(createKnxTable);
    db.exec(createSceneTable);
    db.exec(createSceneItemsTable);
    db.exec(createSceneAddressItemsTable);
    db.exec(createScheduleTable);
    db.exec(createScheduleScenesTable);
    db.exec(createMultiScenesTable);
    db.exec(createMultiSceneScenesTable);
    db.exec(createSequencesTable);
    db.exec(createSequenceMultiScenesTable);
    // Note: unit_output_configs and unit_input_configs tables are removed
  } catch (error) {
    console.error("Failed to create tables:", error);
    throw error;
  }
}
