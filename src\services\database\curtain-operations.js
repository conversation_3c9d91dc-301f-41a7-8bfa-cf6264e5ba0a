import { getProjectItems, getProjectItemById, deleteProjectItem, duplicateProjectItem } from "./generic-operations.js";
import { createProjectItem } from "./lighting-operations.js";
import { updateProjectItem } from "./update-operations.js";

/**
 * Curtain operations
 */
export function getCurtainItems(db, projectId) {
  return getProjectItems(db, projectId, "curtain");
}

export function createCurtainItem(db, projectId, itemData) {
  return createProjectItem(db, projectId, itemData, "curtain");
}

export function updateCurtainItem(db, id, itemData) {
  return updateProjectItem(db, id, itemData, "curtain");
}

export function deleteCurtainItem(db, id) {
  return deleteProjectItem(db, id, "curtain");
}

export function duplicateCurtainItem(db, id) {
  return duplicateProjectItem(db, id, "curtain", createProjectItem, getProjectItemById);
}
