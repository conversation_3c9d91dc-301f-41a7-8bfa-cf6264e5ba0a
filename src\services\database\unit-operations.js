import { getProjectItems, getProjectItemById, deleteProjectItem } from "./generic-operations.js";

/**
 * Unit operations - Special handling for unit table structure
 */
export function getUnitItems(db, projectId) {
  return getProjectItems(db, projectId, "unit");
}

export function createUnitItem(db, projectId, itemData) {
  try {
    const {
      type,
      serial_no,
      ip_address,
      id_can,
      mode,
      firmware_version,
      hardware_version,
      manufacture_date,
      can_load,
      recovery_mode,
      description,
      discovered_at,
      rs485_config,
      input_configs,
      output_configs,
    } = itemData;

    const stmt = db.prepare(`
      INSERT INTO unit (
        project_id, type, serial_no, ip_address,
        id_can, mode, firmware_version, hardware_version,
        manufacture_date, can_load, recovery_mode, description, discovered_at, rs485_config, input_configs, output_configs
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      projectId,
      type,
      serial_no,
      ip_address,
      id_can,
      mode,
      firmware_version,
      hardware_version,
      manufacture_date,
      can_load ? 1 : 0,
      recovery_mode ? 1 : 0,
      description,
      discovered_at,
      rs485_config ? JSON.stringify(rs485_config) : null,
      input_configs ? JSON.stringify(input_configs) : null,
      output_configs ? JSON.stringify(output_configs) : null
    );

    return getProjectItemById(db, result.lastInsertRowid, "unit");
  } catch (error) {
    console.error("Failed to create unit item:", error);
    throw error;
  }
}

export function updateUnitItem(db, id, itemData) {
  try {
    const {
      type,
      serial_no,
      ip_address,
      id_can,
      mode,
      firmware_version,
      hardware_version,
      manufacture_date,
      can_load,
      recovery_mode,
      description,
      discovered_at,
      rs485_config,
      input_configs,
      output_configs,
    } = itemData;

    const stmt = db.prepare(`
      UPDATE unit
      SET type = ?, serial_no = ?, ip_address = ?,
          id_can = ?, mode = ?, firmware_version = ?, hardware_version = ?,
          manufacture_date = ?, can_load = ?, recovery_mode = ?, description = ?,
          discovered_at = ?, rs485_config = ?, input_configs = ?, output_configs = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    const result = stmt.run(
      type,
      serial_no,
      ip_address,
      id_can,
      mode,
      firmware_version,
      hardware_version,
      manufacture_date,
      can_load ? 1 : 0,
      recovery_mode ? 1 : 0,
      description,
      discovered_at,
      rs485_config ? JSON.stringify(rs485_config) : null,
      input_configs ? JSON.stringify(input_configs) : null,
      output_configs ? JSON.stringify(output_configs) : null,
      id
    );

    if (result.changes === 0) {
      throw new Error("Unit item not found");
    }

    return getProjectItemById(db, id, "unit");
  } catch (error) {
    console.error("Failed to update unit item:", error);
    throw error;
  }
}

export function deleteUnitItem(db, id) {
  return deleteProjectItem(db, id, "unit");
}

export function duplicateUnitItem(db, id) {
  try {
    const originalItem = getProjectItemById(db, id, "unit");

    if (!originalItem) {
      throw new Error("Unit item not found");
    }

    const duplicatedItem = {
      type: originalItem.type,
      serial_no: originalItem.serial_no,
      ip_address: originalItem.ip_address,
      id_can: originalItem.id_can,
      mode: originalItem.mode,
      firmware_version: originalItem.firmware_version,
      description: originalItem.description,
    };

    return createUnitItem(db, originalItem.project_id, duplicatedItem);
  } catch (error) {
    console.error("Failed to duplicate unit item:", error);
    throw error;
  }
}
