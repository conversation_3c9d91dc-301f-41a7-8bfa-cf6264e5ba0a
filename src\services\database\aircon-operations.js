import { getProjectItems, getProjectItemById, deleteProjectItem, duplicateProjectItem } from "./generic-operations.js";
import { createProjectItem } from "./lighting-operations.js";
import { updateProjectItem } from "./update-operations.js";
import { findNextAvailableAddress } from "./utils.js";

/**
 * Aircon operations - Special handling for aircon cards and items
 */
export function getAirconItems(db, projectId) {
  return getProjectItems(db, projectId, "aircon");
}

// Get aircon cards (each item is now a card)
export function getAirconCards(db, projectId) {
  try {
    const items = db
      .prepare("SELECT * FROM aircon WHERE project_id = ? ORDER BY address")
      .all(projectId);

    // Each item is now a card
    return items.map((item) => ({
      name: item.name,
      address: item.address,
      description: item.description,
      item: item,
    }));
  } catch (error) {
    console.error("Failed to get aircon cards:", error);
    throw error;
  }
}

// Create a new aircon card (creates 1 item with general aircon type)
export function createAirconCard(db, projectId, cardData) {
  try {
    const { name, address, description } = cardData;

    // Ensure address is treated as string for consistency
    const addressStr = address.toString();

    // Check if address already exists for this project
    const existingItems = db
      .prepare(
        "SELECT COUNT(*) as count FROM aircon WHERE project_id = ? AND address = ?"
      )
      .get(projectId, addressStr);

    if (existingItems.count > 0) {
      throw new Error(`Address ${addressStr} already exists.`);
    }

    // Create single aircon item
    const itemData = {
      name,
      address: addressStr,
      description,
      label: "Aircon",
    };

    const item = createProjectItem(db, projectId, itemData, "aircon");
    return [item]; // Return as array for compatibility
  } catch (error) {
    console.error("Failed to create aircon card:", error);
    throw error;
  }
}

export function createAirconItem(db, projectId, itemData) {
  return createProjectItem(db, projectId, itemData, "aircon");
}

export function updateAirconItem(db, id, itemData) {
  return updateProjectItem(db, id, itemData, "aircon");
}

export function deleteAirconItem(db, id) {
  return deleteProjectItem(db, id, "aircon");
}

// Delete entire aircon card (all items with same address)
export function deleteAirconCard(db, projectId, address) {
  try {
    const stmt = db.prepare(
      "DELETE FROM aircon WHERE project_id = ? AND address = ?"
    );
    const result = stmt.run(projectId, address);

    return { success: true, deletedCount: result.changes };
  } catch (error) {
    console.error("Failed to delete aircon card:", error);
    throw error;
  }
}

export function duplicateAirconItem(db, id) {
  return duplicateProjectItem(db, id, "aircon", createProjectItem, getProjectItemById);
}

// Duplicate entire aircon card
export function duplicateAirconCard(db, projectId, address) {
  try {
    const item = db
      .prepare("SELECT * FROM aircon WHERE project_id = ? AND address = ?")
      .get(projectId, address);

    if (!item) {
      throw new Error("Aircon card not found");
    }

    // Find a unique numeric address for the duplicated card in range 1-255
    const newAddress = findNextAvailableAddress(db, projectId, "aircon");

    const duplicatedItem = {
      name: item.name ? `${item.name} (Copy)` : null,
      address: newAddress,
      description: item.description,
      label: item.label,
    };

    const newItem = createProjectItem(
      db,
      projectId,
      duplicatedItem,
      "aircon"
    );
    return [newItem]; // Return as array for compatibility
  } catch (error) {
    console.error("Failed to duplicate aircon card:", error);
    throw error;
  }
}
