import path from "node:path";
import fs from "node:fs";
import { app } from "electron";
import { createTables } from "./database/table-schemas.js";
import { getObjectValue } from "./database/utils.js";

// Import all operations
import * as GenericOps from "./database/generic-operations.js";
import * as ProjectOps from "./database/project-operations.js";
import * as LightingOps from "./database/lighting-operations.js";
import * as UpdateOps from "./database/update-operations.js";
import * as AirconOps from "./database/aircon-operations.js";
import * as UnitOps from "./database/unit-operations.js";
import * as UnitIOOps from "./database/unit-io-operations.js";
import * as CurtainOps from "./database/curtain-operations.js";
import * as KnxOps from "./database/knx-operations.js";
import * as SceneOps from "./database/scene-operations.js";
import * as ScheduleOps from "./database/schedule-operations.js";
import * as MultiSceneOps from "./database/multi-scene-operations.js";
import * as SequenceOps from "./database/sequence-operations.js";
import * as CopyOps from "./database/copy-operations.js";

class DatabaseService {
    constructor() {
        this.db = null;
        this.init();
    }

    // Helper functions
    getObjectValue(objectType) {
        return getObjectValue(objectType);
    }

    // Database initialization
    init() {
        try {
            // Dynamic import for better-sqlite3
            const Database = require("better-sqlite3");

            // Tạo đường dẫn database trong documents directory
            const documentsPath = app.getPath("documents");
            const toolkitPath = path.join(documentsPath, "Toolkit Engine");

            // Tạo thư mục nếu chưa tồn tại
            if (!fs.existsSync(toolkitPath)) {
                fs.mkdirSync(toolkitPath, { recursive: true });
            }

            // Tạo đường dẫn database
            const dbPath = path.join(toolkitPath, "projects.db");

            // Khởi tạo database
            this.db = new Database(dbPath);

            // Tạo bảng projects nếu chưa tồn tại
            this.createTables();
        } catch (error) {
            console.error("Failed to initialize database:", error);
            throw error;
        }
    }

    createTables() {
        createTables(this.db);
    }

    // Generic operations
    findNextAvailableAddress(projectId, tableName) {
        return GenericOps.findNextAvailableAddress(this.db, projectId, tableName);
    }

    getAllProjectItems(projectId) {
        return GenericOps.getAllProjectItems(this.db, projectId);
    }

    getProjectItems(projectId, tableName) {
        return GenericOps.getProjectItems(this.db, projectId, tableName);
    }

    getProjectItemById(id, tableName) {
        return GenericOps.getProjectItemById(this.db, id, tableName);
    }

    createProjectItem(projectId, itemData, tableName) {
        return LightingOps.createProjectItem(this.db, projectId, itemData, tableName);
    }

    updateProjectItem(id, itemData, tableName) {
        return UpdateOps.updateProjectItem(this.db, id, itemData, tableName);
    }

    deleteProjectItem(id, tableName) {
        return GenericOps.deleteProjectItem(this.db, id, tableName);
    }

    duplicateProjectItem(id, tableName) {
        return GenericOps.duplicateProjectItem(this.db, id, tableName, LightingOps.createProjectItem, GenericOps.getProjectItemById);
    }

    // Project operations
    getAllProjects() {
        return ProjectOps.getAllProjects(this.db);
    }

    getProjectById(id) {
        return ProjectOps.getProjectById(this.db, id);
    }

    createProject(projectData) {
        return ProjectOps.createProject(this.db, projectData);
    }

    updateProject(id, projectData) {
        return ProjectOps.updateProject(this.db, id, projectData);
    }

    deleteProject(id) {
        return ProjectOps.deleteProject(this.db, id);
    }

    duplicateProject(id) {
        return ProjectOps.duplicateProject(this.db, id, this.createProject.bind(this), CopyOps.copyProjectItems.bind(null, this.db));
    }

    importProject(projectData, itemsData) {
        return ProjectOps.importProject(this.db, projectData, itemsData, this.createProject.bind(this), this.createUnitItem.bind(this), this.createProjectItem.bind(this), this.createScheduleItem.bind(this));
    }

    bulkImportItems(projectId, items, category) {
        return ProjectOps.bulkImportItems(this.db, projectId, items, category, this.createUnitItem.bind(this), this.createProjectItem.bind(this), this.createScheduleItem.bind(this));
    }

    // Lighting operations
    getLightingItems(projectId) {
        return LightingOps.getLightingItems(this.db, projectId);
    }

    createLightingItem(projectId, itemData) {
        return LightingOps.createLightingItem(this.db, projectId, itemData);
    }

    updateLightingItem(id, itemData) {
        return LightingOps.updateLightingItem(this.db, id, itemData);
    }

    deleteLightingItem(id) {
        return LightingOps.deleteLightingItem(this.db, id);
    }

    duplicateLightingItem(id) {
        return LightingOps.duplicateLightingItem(this.db, id);
    }

    // Aircon operations
    getAirconItems(projectId) {
        return AirconOps.getAirconItems(this.db, projectId);
    }

    getAirconCards(projectId) {
        return AirconOps.getAirconCards(this.db, projectId);
    }

    createAirconCard(projectId, cardData) {
        return AirconOps.createAirconCard(this.db, projectId, cardData);
    }

    createAirconItem(projectId, itemData) {
        return AirconOps.createAirconItem(this.db, projectId, itemData);
    }

    updateAirconItem(id, itemData) {
        return AirconOps.updateAirconItem(this.db, id, itemData);
    }

    deleteAirconItem(id) {
        return AirconOps.deleteAirconItem(this.db, id);
    }

    deleteAirconCard(projectId, address) {
        return AirconOps.deleteAirconCard(this.db, projectId, address);
    }

    duplicateAirconItem(id) {
        return AirconOps.duplicateAirconItem(this.db, id);
    }

    duplicateAirconCard(projectId, address) {
        return AirconOps.duplicateAirconCard(this.db, projectId, address);
    }

    // Unit operations
    getUnitItems(projectId) {
        return UnitOps.getUnitItems(this.db, projectId);
    }

    createUnitItem(projectId, itemData) {
        return UnitOps.createUnitItem(this.db, projectId, itemData);
    }

    updateUnitItem(id, itemData) {
        return UnitOps.updateUnitItem(this.db, id, itemData);
    }

    deleteUnitItem(id) {
        return UnitOps.deleteUnitItem(this.db, id);
    }

    duplicateUnitItem(id) {
        return UnitOps.duplicateUnitItem(this.db, id);
    }

    // Unit I/O operations
    getUnitInputConfig(unitId, inputIndex) {
        return UnitIOOps.getUnitInputConfig(this.db, unitId, inputIndex);
    }

    getUnitOutputConfig(unitId, outputIndex) {
        return UnitIOOps.getUnitOutputConfig(this.db, unitId, outputIndex);
    }

    saveUnitInputConfig(unitId, inputIndex, functionValue, lightingId, multiGroupConfig, rlcConfig) {
        return UnitIOOps.saveUnitInputConfig(this.db, unitId, inputIndex, functionValue, lightingId, multiGroupConfig, rlcConfig);
    }

    saveUnitOutputConfig(unitId, outputIndex, outputType, configData) {
        return UnitIOOps.saveUnitOutputConfig(this.db, unitId, outputIndex, outputType, configData);
    }

    getAllUnitInputConfigs(unitId) {
        return UnitIOOps.getAllUnitInputConfigs(this.db, unitId);
    }

    getAllUnitOutputConfigs(unitId) {
        return UnitIOOps.getAllUnitOutputConfigs(this.db, unitId);
    }

    deleteUnitInputConfig(unitId, inputIndex) {
        return UnitIOOps.deleteUnitInputConfig(this.db, unitId, inputIndex);
    }

    deleteUnitOutputConfig(unitId, outputIndex) {
        return UnitIOOps.deleteUnitOutputConfig(this.db, unitId, outputIndex);
    }

    clearAllUnitIOConfigs(unitId) {
        return UnitIOOps.clearAllUnitIOConfigs(this.db, unitId);
    }

    // Curtain operations
    getCurtainItems(projectId) {
        return CurtainOps.getCurtainItems(this.db, projectId);
    }

    createCurtainItem(projectId, itemData) {
        return CurtainOps.createCurtainItem(this.db, projectId, itemData);
    }

    updateCurtainItem(id, itemData) {
        return CurtainOps.updateCurtainItem(this.db, id, itemData);
    }

    deleteCurtainItem(id) {
        return CurtainOps.deleteCurtainItem(this.db, id);
    }

    duplicateCurtainItem(id) {
        return CurtainOps.duplicateCurtainItem(this.db, id);
    }

    // KNX operations
    getKnxItems(projectId) {
        return KnxOps.getKnxItems(this.db, projectId);
    }

    createKnxItem(projectId, itemData) {
        return KnxOps.createKnxItem(this.db, projectId, itemData);
    }

    updateKnxItem(id, itemData) {
        return KnxOps.updateKnxItem(this.db, id, itemData);
    }

    deleteKnxItem(id) {
        return KnxOps.deleteKnxItem(this.db, id);
    }

    duplicateKnxItem(id) {
        return KnxOps.duplicateKnxItem(this.db, id);
    }

    // Scene operations
    getSceneItems(projectId) {
        return SceneOps.getSceneItems(this.db, projectId);
    }

    createSceneItem(projectId, itemData) {
        return SceneOps.createSceneItem(this.db, projectId, itemData);
    }

    updateSceneItem(id, itemData) {
        return SceneOps.updateSceneItem(this.db, id, itemData);
    }

    deleteSceneItem(id) {
        return SceneOps.deleteSceneItem(this.db, id);
    }

    duplicateSceneItem(id) {
        return SceneOps.duplicateSceneItem(this.db, id);
    }

    getSceneItemsBySceneId(sceneId) {
        return SceneOps.getSceneItemsBySceneId(this.db, sceneId);
    }

    createSceneItemBatch(sceneId, items) {
        return SceneOps.createSceneItemBatch(this.db, sceneId, items);
    }

    deleteSceneItemsBySceneId(sceneId) {
        return SceneOps.deleteSceneItemsBySceneId(this.db, sceneId);
    }

    getSceneAddressItems(projectId) {
        return SceneOps.getSceneAddressItems(this.db, projectId);
    }

    createSceneAddressItem(projectId, itemData) {
        return SceneOps.createSceneAddressItem(this.db, projectId, itemData);
    }

    deleteSceneAddressItem(projectId, address, itemType, itemId, objectType) {
        return SceneOps.deleteSceneAddressItem(this.db, projectId, address, itemType, itemId, objectType);
    }

    deleteSceneAddressItemsByItemId(itemType, itemId) {
        return SceneOps.deleteSceneAddressItemsByItemId(this.db, itemType, itemId);
    }

    // Schedule operations
    getScheduleItems(projectId) {
        return ScheduleOps.getScheduleItems(this.db, projectId);
    }

    createScheduleItem(projectId, itemData) {
        return ScheduleOps.createScheduleItem(this.db, projectId, itemData);
    }

    updateScheduleItem(id, itemData) {
        return ScheduleOps.updateScheduleItem(this.db, id, itemData);
    }

    deleteScheduleItem(id) {
        return ScheduleOps.deleteScheduleItem(this.db, id);
    }

    duplicateScheduleItem(id) {
        return ScheduleOps.duplicateScheduleItem(this.db, id);
    }

    getScheduleItemById(id) {
        return ScheduleOps.getScheduleItemById(this.db, id);
    }

    getScheduleItemsWithScenes(projectId) {
        return ScheduleOps.getScheduleItemsWithScenes(this.db, projectId);
    }

    // Multi-scene operations
    getMultiSceneItems(projectId) {
        return MultiSceneOps.getMultiSceneItems(this.db, projectId);
    }

    createMultiSceneItem(projectId, itemData) {
        return MultiSceneOps.createMultiSceneItem(this.db, projectId, itemData);
    }

    updateMultiSceneItem(id, itemData) {
        return MultiSceneOps.updateMultiSceneItem(this.db, id, itemData);
    }

    deleteMultiSceneItem(id) {
        return MultiSceneOps.deleteMultiSceneItem(this.db, id);
    }

    duplicateMultiSceneItem(id) {
        return MultiSceneOps.duplicateMultiSceneItem(this.db, id);
    }

    getMultiSceneScenes(multiSceneId) {
        return MultiSceneOps.getMultiSceneScenes(this.db, multiSceneId);
    }

    updateMultiSceneScenes(multiSceneId, sceneIds) {
        return MultiSceneOps.updateMultiSceneScenes(this.db, multiSceneId, sceneIds);
    }

    deleteMultiSceneScenes(multiSceneId) {
        return MultiSceneOps.deleteMultiSceneScenes(this.db, multiSceneId);
    }

    getMultiSceneItemById(id) {
        return MultiSceneOps.getMultiSceneItemById(this.db, id);
    }

    getMultiSceneItemsWithScenes(projectId) {
        return MultiSceneOps.getMultiSceneItemsWithScenes(this.db, projectId);
    }

    // Sequence operations
    getSequenceItems(projectId) {
        return SequenceOps.getSequenceItems(this.db, projectId);
    }

    createSequenceItem(projectId, itemData) {
        return SequenceOps.createSequenceItem(this.db, projectId, itemData);
    }

    updateSequenceItem(id, itemData) {
        return SequenceOps.updateSequenceItem(this.db, id, itemData);
    }

    deleteSequenceItem(id) {
        return SequenceOps.deleteSequenceItem(this.db, id);
    }

    duplicateSequenceItem(id) {
        return SequenceOps.duplicateSequenceItem(this.db, id);
    }

    getSequenceMultiScenes(sequenceId) {
        return SequenceOps.getSequenceMultiScenes(this.db, sequenceId);
    }

    updateSequenceMultiScenes(sequenceId, multiSceneIds) {
        return SequenceOps.updateSequenceMultiScenes(this.db, sequenceId, multiSceneIds);
    }

    deleteSequenceMultiScenes(sequenceId) {
        return SequenceOps.deleteSequenceMultiScenes(this.db, sequenceId);
    }

    getSequenceItemById(id) {
        return SequenceOps.getSequenceItemById(this.db, id);
    }

    getSequenceItemsWithMultiScenes(projectId) {
        return SequenceOps.getSequenceItemsWithMultiScenes(this.db, projectId);
    }
}

// Create and export a singleton instance
const databaseService = new DatabaseService();

export default databaseService;
