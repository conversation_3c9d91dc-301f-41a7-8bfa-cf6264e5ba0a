import { getProjectItems, getProjectItemById, deleteProjectItem, duplicateProjectItem } from "./generic-operations.js";
import { createProjectItem } from "./lighting-operations.js";
import { updateProjectItem } from "./update-operations.js";

/**
 * Sequence operations
 */
export function getSequenceItems(db, projectId) {
  return getProjectItems(db, projectId, "sequences");
}

export function createSequenceItem(db, projectId, itemData) {
  return createProjectItem(db, projectId, itemData, "sequences");
}

export function updateSequenceItem(db, id, itemData) {
  return updateProjectItem(db, id, itemData, "sequences");
}

export function deleteSequenceItem(db, id) {
  return deleteProjectItem(db, id, "sequences");
}

export function duplicateSequenceItem(db, id) {
  return duplicateProjectItem(db, id, "sequences", createProjectItem, getProjectItemById);
}

// Sequence multi-scene operations
export function getSequenceMultiScenes(db, sequenceId) {
  try {
    const stmt = db.prepare(`
      SELECT ms.id, ms.name, ms.address, ms.type, ms.description, sms.multi_scene_order
      FROM multi_scenes ms
      INNER JOIN sequence_multi_scenes sms ON ms.id = sms.multi_scene_id
      WHERE sms.sequence_id = ?
      ORDER BY sms.multi_scene_order ASC
    `);
    return stmt.all(sequenceId);
  } catch (error) {
    console.error("Failed to get sequence multi-scenes:", error);
    throw error;
  }
}

export function updateSequenceMultiScenes(db, sequenceId, multiSceneIds) {
  try {
    const transaction = db.transaction(() => {
      // First, delete existing associations
      const deleteStmt = db.prepare("DELETE FROM sequence_multi_scenes WHERE sequence_id = ?");
      deleteStmt.run(sequenceId);

      // Then add new associations with order
      if (multiSceneIds && multiSceneIds.length > 0) {
        const insertStmt = db.prepare(`
          INSERT INTO sequence_multi_scenes (sequence_id, multi_scene_id, multi_scene_order)
          VALUES (?, ?, ?)
        `);

        multiSceneIds.forEach((multiSceneId, index) => {
          insertStmt.run(sequenceId, multiSceneId, index);
        });
      }

      return getSequenceMultiScenes(db, sequenceId);
    });

    return transaction();
  } catch (error) {
    console.error("Failed to update sequence multi-scenes:", error);
    throw error;
  }
}

export function deleteSequenceMultiScenes(db, sequenceId) {
  try {
    const stmt = db.prepare("DELETE FROM sequence_multi_scenes WHERE sequence_id = ?");
    const result = stmt.run(sequenceId);
    return { success: true, deletedCount: result.changes };
  } catch (error) {
    console.error("Failed to delete sequence multi-scenes:", error);
    throw error;
  }
}

// Get sequence item with multi-scenes
export function getSequenceItemById(db, id) {
  try {
    const sequence = getProjectItemById(db, id, "sequences");
    if (!sequence) {
      return null;
    }

    // Get associated multi-scenes
    sequence.multiScenes = getSequenceMultiScenes(db, id);

    return sequence;
  } catch (error) {
    console.error("Failed to get sequence item by id:", error);
    throw error;
  }
}

// Get all sequence items with multi-scenes
export function getSequenceItemsWithMultiScenes(db, projectId) {
  try {
    const sequences = getSequenceItems(db, projectId);

    return sequences.map((sequence) => {
      // Get associated multi-scenes
      sequence.multiScenes = getSequenceMultiScenes(db, sequence.id);
      return sequence;
    });
  } catch (error) {
    console.error("Failed to get sequence items with multi-scenes:", error);
    throw error;
  }
}
