import { getProjectItemById } from "./generic-operations.js";
import { updateUnitItem } from "./unit-operations.js";

/**
 * Unit I/O Configuration methods (JSON-based)
 */
export function getUnitInputConfig(db, unitId, inputIndex) {
  try {
    const unit = getProjectItemById(db, unitId, "unit");
    if (!unit || !unit.input_configs) {
      return null;
    }

    const inputConfigs = unit.input_configs;
    const inputConfig = inputConfigs.inputs?.find(input => input.index === inputIndex);

    if (inputConfig) {
      return {
        unit_id: unitId,
        input_index: inputIndex,
        function_value: inputConfig.function_value || 0,
        lighting_id: inputConfig.lighting_id || null,
        multi_group_config: inputConfig.multi_group_config || [],
        rlc_config: inputConfig.rlc_config || {},
      };
    }
    return null;
  } catch (error) {
    console.error("Failed to get unit input config:", error);
    throw error;
  }
}

export function getUnitOutputConfig(db, unitId, outputIndex) {
  try {
    const unit = getProjectItemById(db, unitId, "unit");
    if (!unit || !unit.output_configs) {
      return null;
    }

    const outputConfigs = unit.output_configs;
    const outputConfig = outputConfigs.outputs?.find(output => output.index === outputIndex);

    if (outputConfig) {
      return {
        unit_id: unitId,
        output_index: outputIndex,
        output_type: outputConfig.type,
        config_data: outputConfig.config || {},
      };
    }
    return null;
  } catch (error) {
    console.error("Failed to get unit output config:", error);
    throw error;
  }
}

export function saveUnitInputConfig(
  db,
  unitId,
  inputIndex,
  functionValue,
  lightingId,
  multiGroupConfig,
  rlcConfig
) {
  try {
    const unit = getProjectItemById(db, unitId, "unit");
    if (!unit) {
      throw new Error("Unit not found");
    }

    // Get current input configs or create new structure
    let inputConfigs = unit.input_configs || { inputs: [] };

    // Find existing input config or create new one
    const existingIndex = inputConfigs.inputs.findIndex(input => input.index === inputIndex);
    const inputConfig = {
      index: inputIndex,
      function_value: functionValue || 0,
      lighting_id: lightingId || null,
      multi_group_config: multiGroupConfig || [],
      rlc_config: rlcConfig || {},
    };

    if (existingIndex >= 0) {
      inputConfigs.inputs[existingIndex] = inputConfig;
    } else {
      inputConfigs.inputs.push(inputConfig);
    }

    // Sort by index
    inputConfigs.inputs.sort((a, b) => a.index - b.index);

    // Update unit with new input configs
    const result = updateUnitItem(db, unitId, {
      ...unit,
      input_configs: inputConfigs,
    });

    return getUnitInputConfig(db, unitId, inputIndex);
  } catch (error) {
    console.error("Failed to save unit input config:", error);
    throw error;
  }
}

export function saveUnitOutputConfig(db, unitId, outputIndex, outputType, configData) {
  try {
    const unit = getProjectItemById(db, unitId, "unit");
    if (!unit) {
      throw new Error("Unit not found");
    }

    // Get current output configs or create new structure
    let outputConfigs = unit.output_configs || { outputs: [] };

    // Find existing output config or create new one
    const existingIndex = outputConfigs.outputs.findIndex(output => output.index === outputIndex);

    // Remove deviceId, deviceType, and address from config data to avoid duplication
    // For AC outputs, address is equivalent to deviceId and should be stored at output level
    const { deviceId, deviceType, address, ...cleanConfigData } = configData;

    const outputConfig = {
      index: outputIndex,
      type: outputType,
      device_id: deviceId || null,
      device_type: deviceType || (outputType === "ac" ? "aircon" : "lighting"),
      name: cleanConfigData.name || `${outputType} ${outputIndex + 1}`,
      config: cleanConfigData,
    };

    if (existingIndex >= 0) {
      outputConfigs.outputs[existingIndex] = outputConfig;
    } else {
      outputConfigs.outputs.push(outputConfig);
    }

    // Sort by index
    outputConfigs.outputs.sort((a, b) => a.index - b.index);

    // Update unit with new output configs
    const result = updateUnitItem(db, unitId, {
      ...unit,
      output_configs: outputConfigs,
    });

    return getUnitOutputConfig(db, unitId, outputIndex);
  } catch (error) {
    console.error("Failed to save unit output config:", error);
    throw error;
  }
}

export function getAllUnitInputConfigs(db, unitId) {
  try {
    const unit = getProjectItemById(db, unitId, "unit");
    if (!unit || !unit.input_configs) {
      return [];
    }

    const inputConfigs = unit.input_configs;
    return (inputConfigs.inputs || []).map(input => ({
      unit_id: unitId,
      input_index: input.index,
      function_value: input.function_value || 0,
      lighting_id: input.lighting_id || null,
      multi_group_config: input.multi_group_config || [],
      rlc_config: input.rlc_config || {},
    }));
  } catch (error) {
    console.error("Failed to get all unit input configs:", error);
    throw error;
  }
}

export function getAllUnitOutputConfigs(db, unitId) {
  try {
    const unit = getProjectItemById(db, unitId, "unit");
    if (!unit || !unit.output_configs) {
      return [];
    }

    const outputConfigs = unit.output_configs;
    return (outputConfigs.outputs || []).map(output => ({
      unit_id: unitId,
      output_index: output.index,
      output_type: output.type,
      device_id: output.device_id,
      device_type: output.device_type,
      config_data: output.config || {},
    }));
  } catch (error) {
    console.error("Failed to get all unit output configs:", error);
    throw error;
  }
}

export function deleteUnitInputConfig(db, unitId, inputIndex) {
  try {
    const unit = getProjectItemById(db, unitId, "unit");
    if (!unit || !unit.input_configs) {
      return false;
    }

    let inputConfigs = unit.input_configs;
    const originalLength = inputConfigs.inputs?.length || 0;

    // Remove the input config
    inputConfigs.inputs = (inputConfigs.inputs || []).filter(input => input.index !== inputIndex);

    if (inputConfigs.inputs.length < originalLength) {
      // Update unit with new input configs
      updateUnitItem(db, unitId, {
        ...unit,
        input_configs: inputConfigs,
      });
      return true;
    }
    return false;
  } catch (error) {
    console.error("Failed to delete unit input config:", error);
    throw error;
  }
}

export function deleteUnitOutputConfig(db, unitId, outputIndex) {
  try {
    const unit = getProjectItemById(db, unitId, "unit");
    if (!unit || !unit.output_configs) {
      return false;
    }

    let outputConfigs = unit.output_configs;
    const originalLength = outputConfigs.outputs?.length || 0;

    // Remove the output config
    outputConfigs.outputs = (outputConfigs.outputs || []).filter(output => output.index !== outputIndex);

    if (outputConfigs.outputs.length < originalLength) {
      // Update unit with new output configs
      updateUnitItem(db, unitId, {
        ...unit,
        output_configs: outputConfigs,
      });
      return true;
    }
    return false;
  } catch (error) {
    console.error("Failed to delete unit output config:", error);
    throw error;
  }
}

// Clear all I/O configurations for a unit (used when unit type changes)
export function clearAllUnitIOConfigs(db, unitId) {
  try {
    const unit = getProjectItemById(db, unitId, "unit");
    if (!unit) {
      return false;
    }

    // Clear both input and output configs
    updateUnitItem(db, unitId, {
      ...unit,
      input_configs: null,
      output_configs: null,
    });

    return true;
  } catch (error) {
    console.error("Failed to clear all unit I/O configs:", error);
    throw error;
  }
}
