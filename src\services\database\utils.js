import { OBJECT_TYPES } from "../../constants.js";

/**
 * Helper function to get object_value from object_type
 */
export function getObjectValue(objectType) {
  // Find the matching object type and return its obj_value
  for (const [, value] of Object.entries(OBJECT_TYPES)) {
    if (typeof value === "object" && value.obj_name === objectType) {
      return value.obj_value;
    }
  }
  // Default fallback
  console.warn(`Unknown object type: ${objectType}`);
  return 0;
}

/**
 * Helper method to find next available address in range 1-255
 */
export function findNextAvailableAddress(db, projectId, tableName) {
  try {
    const existingAddresses = db
      .prepare(
        `SELECT DISTINCT address FROM ${tableName} WHERE project_id = ?`
      )
      .all(projectId);
    const addressNumbers = existingAddresses
      .map((item) => parseInt(item.address))
      .filter((num) => !isNaN(num) && num >= 1 && num <= 255)
      .sort((a, b) => a - b);

    // Find the first available address in range 1-255
    let newAddress = 1;
    for (const num of addressNumbers) {
      if (newAddress < num) {
        break;
      }
      newAddress = num + 1;
    }

    // If we've exceeded 255, find a gap in the existing addresses
    if (newAddress > 255) {
      newAddress = null;
      for (let i = 1; i <= 255; i++) {
        if (!addressNumbers.includes(i)) {
          newAddress = i;
          break;
        }
      }

      if (newAddress === null) {
        throw new Error(
          `No available addresses in range 1-255 for ${tableName} duplication`
        );
      }
    }

    return newAddress.toString();
  } catch (error) {
    console.error(
      `Failed to find available address for ${tableName}:`,
      error
    );
    throw error;
  }
}
