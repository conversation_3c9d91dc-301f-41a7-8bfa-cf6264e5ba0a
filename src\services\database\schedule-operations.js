import { getProjectItems, getProjectItemById, deleteProjectItem } from "./generic-operations.js";

/**
 * Schedule operations
 */
export function getScheduleItems(db, projectId) {
  return getProjectItems(db, projectId, "schedule");
}

export function createScheduleItem(db, projectId, itemData) {
  try {
    const { name, description, time, days, enabled, scenes } = itemData;

    // Validate required fields
    if (!time) {
      throw new Error("Time is required for schedule.");
    }
    if (!days || days.length === 0) {
      throw new Error("At least one day must be selected for schedule.");
    }

    const transaction = db.transaction(() => {
      // Create schedule
      const stmt = db.prepare(`
        INSERT INTO schedule (project_id, name, description, time, days, enabled)
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      const result = stmt.run(
        projectId,
        name,
        description,
        time,
        JSON.stringify(days),
        enabled ? 1 : 0
      );

      const scheduleId = result.lastInsertRowid;

      // Add scene associations if provided
      if (scenes && scenes.length > 0) {
        const sceneStmt = db.prepare(`
          INSERT INTO schedule_scenes (schedule_id, scene_id)
          VALUES (?, ?)
        `);

        scenes.forEach((sceneId) => {
          sceneStmt.run(scheduleId, sceneId);
        });
      }

      return getScheduleItemById(db, scheduleId);
    });

    return transaction();
  } catch (error) {
    console.error("Failed to create schedule item:", error);
    throw error;
  }
}

export function updateScheduleItem(db, id, itemData) {
  try {
    const { name, description, time, days, enabled, scenes } = itemData;

    // Validate required fields
    if (!time) {
      throw new Error("Time is required for schedule.");
    }
    if (!days || days.length === 0) {
      throw new Error("At least one day must be selected for schedule.");
    }

    const transaction = db.transaction(() => {
      // Update schedule
      const stmt = db.prepare(`
        UPDATE schedule
        SET name = ?, description = ?, time = ?, days = ?, enabled = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);

      const result = stmt.run(
        name,
        description,
        time,
        JSON.stringify(days),
        enabled ? 1 : 0,
        id
      );

      if (result.changes === 0) {
        throw new Error("Schedule item not found");
      }

      // Update scene associations
      // First, delete existing associations
      const deleteStmt = db.prepare("DELETE FROM schedule_scenes WHERE schedule_id = ?");
      deleteStmt.run(id);

      // Then add new associations if provided
      if (scenes && scenes.length > 0) {
        const sceneStmt = db.prepare(`
          INSERT INTO schedule_scenes (schedule_id, scene_id)
          VALUES (?, ?)
        `);

        scenes.forEach((sceneId) => {
          sceneStmt.run(id, sceneId);
        });
      }

      return getScheduleItemById(db, id);
    });

    return transaction();
  } catch (error) {
    console.error("Failed to update schedule item:", error);
    throw error;
  }
}

export function deleteScheduleItem(db, id) {
  return deleteProjectItem(db, id, "schedule");
}

export function duplicateScheduleItem(db, id) {
  try {
    const originalItem = getScheduleItemById(db, id);

    if (!originalItem) {
      throw new Error("Schedule item not found");
    }

    const duplicatedItem = {
      name: originalItem.name ? `${originalItem.name} (Copy)` : null,
      description: originalItem.description,
      time: originalItem.time,
      days: originalItem.days,
      enabled: originalItem.enabled,
      scenes: originalItem.scenes,
    };

    return createScheduleItem(db, originalItem.project_id, duplicatedItem);
  } catch (error) {
    console.error("Failed to duplicate schedule item:", error);
    throw error;
  }
}

// Get schedule item with scenes
export function getScheduleItemById(db, id) {
  try {
    const schedule = getProjectItemById(db, id, "schedule");
    if (!schedule) {
      return null;
    }

    // Parse days from JSON
    if (schedule.days) {
      schedule.days = JSON.parse(schedule.days);
    }

    // Get associated scenes
    const sceneStmt = db.prepare(`
      SELECT s.id, s.name, s.address, s.description
      FROM scene s
      INNER JOIN schedule_scenes ss ON s.id = ss.scene_id
      WHERE ss.schedule_id = ?
      ORDER BY s.name
    `);
    schedule.scenes = sceneStmt.all(id);

    return schedule;
  } catch (error) {
    console.error("Failed to get schedule item by id:", error);
    throw error;
  }
}

// Get all schedule items with scenes
export function getScheduleItemsWithScenes(db, projectId) {
  try {
    const schedules = getScheduleItems(db, projectId);

    return schedules.map((schedule) => {
      // Parse days from JSON
      if (schedule.days) {
        schedule.days = JSON.parse(schedule.days);
      }

      // Get associated scenes
      const sceneStmt = db.prepare(`
        SELECT s.id, s.name, s.address, s.description
        FROM scene s
        INNER JOIN schedule_scenes ss ON s.id = ss.scene_id
        WHERE ss.schedule_id = ?
        ORDER BY s.name
      `);
      schedule.scenes = sceneStmt.all(schedule.id);

      return schedule;
    });
  } catch (error) {
    console.error("Failed to get schedule items with scenes:", error);
    throw error;
  }
}
