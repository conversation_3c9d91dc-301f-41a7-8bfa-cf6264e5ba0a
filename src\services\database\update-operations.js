import { getProjectItemById } from "./generic-operations.js";
import { getObjectValue } from "./utils.js";

/**
 * Generic update project item function
 */
export function updateProjectItem(db, id, itemData, tableName) {
  try {
    const {
      name,
      address,
      description,
      object_type,
      label,
      curtain_type,
      curtain_value,
      open_group_id,
      close_group_id,
      stop_group_id,
      pause_period,
      transition_period,
      type,
      factor,
      feedback,
      rcu_group_id,
      knx_switch_group,
      knx_dimming_group,
      knx_value_group,
    } = itemData;

    // Special validation for aircon to prevent duplicate addresses
    if (tableName === "aircon" && address) {
      const currentItem = getProjectItemById(db, id, tableName);
      if (currentItem && currentItem.address !== address) {
        // Check if new address already exists for this project (excluding current item's address)
        const existingItems = db
          .prepare(
            "SELECT COUNT(*) as count FROM aircon WHERE project_id = ? AND address = ? AND address != ?"
          )
          .get(currentItem.project_id, address, currentItem.address);
        if (existingItems.count > 0) {
          throw new Error(`Address ${address} already exists.`);
        }
      }
    }

    // Special validation for lighting to prevent duplicate addresses
    if (tableName === "lighting" && address) {
      const currentItem = getProjectItemById(db, id, tableName);
      if (currentItem && currentItem.address !== address) {
        // Check if new address already exists for this project (excluding current item's address)
        const existingItems = db
          .prepare(
            "SELECT COUNT(*) as count FROM lighting WHERE project_id = ? AND address = ? AND id != ?"
          )
          .get(currentItem.project_id, address, id);
        if (existingItems.count > 0) {
          throw new Error(`Address ${address} already exists.`);
        }
      }
    }

    // Special validation for curtain to prevent duplicate addresses
    if (tableName === "curtain" && address) {
      const currentItem = getProjectItemById(db, id, tableName);
      if (currentItem && currentItem.address !== address) {
        // Check if new address already exists for this project (excluding current item's address)
        const existingItems = db
          .prepare(
            "SELECT COUNT(*) as count FROM curtain WHERE project_id = ? AND address = ? AND id != ?"
          )
          .get(currentItem.project_id, address, id);
        if (existingItems.count > 0) {
          throw new Error(`Address ${address} already exists.`);
        }
      }
    }

    // Special validation for scene - address is required and name length check
    if (tableName === "scene") {
      if (!address || !address.trim()) {
        throw new Error("Address is required for scene.");
      }
      if (!name || name.length > 15) {
        throw new Error(
          "Scene name is required and must be 15 characters or less."
        );
      }
    }

    // Special validation for KNX
    if (tableName === "knx") {
      // Validate address range
      if (address < 0 || address > 511) {
        throw new Error("KNX address must be between 0 and 511.");
      }

      // Check for duplicate addresses
      const currentItem = getProjectItemById(db, id, tableName);
      if (currentItem && currentItem.address !== address) {
        const existingItems = db
          .prepare(
            "SELECT COUNT(*) as count FROM knx WHERE project_id = ? AND address = ? AND id != ?"
          )
          .get(currentItem.project_id, address, id);
        if (existingItems.count > 0) {
          throw new Error(`KNX address ${address} already exists.`);
        }
      }

      // Validate factor
      if (factor && factor < 1) {
        throw new Error("Factor must be greater than or equal to 1.");
      }
    }

    // For aircon table, include label column
    if (tableName === "aircon") {
      // Get current item to check if address is changing
      const currentItem = getProjectItemById(db, id, tableName);

      const stmt = db.prepare(`
        UPDATE ${tableName}
        SET name = ?, address = ?, description = ?, label = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      const result = stmt.run(name, address, description, label, id);

      if (result.changes === 0) {
        throw new Error(`${tableName} item not found`);
      }

      // If address changed, update scene items that reference this aircon item
      if (currentItem && currentItem.address !== address) {
        console.log(
          `Updating scene items for aircon ${id}: ${currentItem.address} -> ${address}`
        );
        updateSceneItemsAddress(db, id, "aircon", address);
      }
    } else if (tableName === "curtain") {
      // For curtain table, include curtain-specific fields
      // Address is required for curtain items
      if (!address) {
        throw new Error("Address is required for curtain items.");
      }

      // Get current item to check if address is changing
      const currentItem = getProjectItemById(db, id, tableName);

      const object_value = getObjectValue(object_type);

      const stmt = db.prepare(`
        UPDATE ${tableName}
        SET name = ?, address = ?, description = ?, object_type = ?, object_value = ?, curtain_type = ?, curtain_value = ?, open_group_id = ?, close_group_id = ?, stop_group_id = ?, pause_period = ?, transition_period = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      const result = stmt.run(
        name,
        address,
        description,
        object_type,
        object_value,
        curtain_type,
        curtain_value || 3,
        open_group_id || null,
        close_group_id || null,
        stop_group_id || null,
        pause_period || 0,
        transition_period || 0,
        id
      );

      if (result.changes === 0) {
        throw new Error(`${tableName} item not found`);
      }

      // If address changed, update scene items that reference this curtain item
      if (currentItem && currentItem.address !== address) {
        console.log(
          `Updating scene items for curtain ${id}: ${currentItem.address} -> ${address}`
        );
        updateSceneItemsAddress(db, id, "curtain", address);
      }
    } else if (tableName === "knx") {
      // For knx table, use new KNX-specific fields
      const stmt = db.prepare(`
        UPDATE ${tableName}
        SET name = ?, address = ?, type = ?, factor = ?, feedback = ?, rcu_group_id = ?, knx_switch_group = ?, knx_dimming_group = ?, knx_value_group = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      const result = stmt.run(
        name,
        address,
        type || 0,
        factor || 2,
        feedback || 0,
        rcu_group_id || null,
        knx_switch_group || null,
        knx_dimming_group || null,
        knx_value_group || null,
        description,
        id
      );

      if (result.changes === 0) {
        throw new Error(`${tableName} item not found`);
      }
    } else if (tableName === "scene") {
      // For scene table, don't use object_type and object_value
      const stmt = db.prepare(`
        UPDATE ${tableName}
        SET name = ?, address = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      const result = stmt.run(name, address, description, id);

      if (result.changes === 0) {
        throw new Error(`${tableName} item not found`);
      }
    } else if (tableName === "multi_scenes") {
      // For multi_scenes table, use type and address fields
      const { type, address } = itemData;

      // Validation for multi_scenes
      if (!name || !name.trim()) {
        throw new Error("Name is required for multi-scene.");
      }
      if (name.length > 15) {
        throw new Error("Multi-scene name must be 15 characters or less.");
      }
      if (!address || !address.trim()) {
        throw new Error("Address is required for multi-scene.");
      }

      const stmt = db.prepare(`
        UPDATE ${tableName}
        SET name = ?, address = ?, type = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      const result = stmt.run(name, address, type || 0, description, id);

      if (result.changes === 0) {
        throw new Error(`${tableName} item not found`);
      }
    } else if (tableName === "sequences") {
      // For sequences table, use address field
      const { address } = itemData;

      // Validation for sequences
      if (!name || !name.trim()) {
        throw new Error("Name is required for sequence.");
      }
      if (name.length > 15) {
        throw new Error("Sequence name must be 15 characters or less.");
      }
      if (!address || !address.trim()) {
        throw new Error("Address is required for sequence.");
      }

      const stmt = db.prepare(`
        UPDATE ${tableName}
        SET name = ?, address = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      const result = stmt.run(name, address, description, id);

      if (result.changes === 0) {
        throw new Error(`${tableName} item not found`);
      }
    } else {
      // For other tables (like lighting), use original structure with object_type and object_value
      // Get current item to check if address is changing
      const currentItem = getProjectItemById(db, id, tableName);

      const object_value = getObjectValue(object_type);
      const stmt = db.prepare(`
        UPDATE ${tableName}
        SET name = ?, address = ?, description = ?, object_type = ?, object_value = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      const result = stmt.run(
        name,
        address,
        description,
        object_type,
        object_value,
        id
      );

      if (result.changes === 0) {
        throw new Error(`${tableName} item not found`);
      }

      // If address changed and this is a table that can be used in scenes, update scene items
      if (
        currentItem &&
        currentItem.address !== address &&
        tableName === "lighting"
      ) {
        console.log(
          `Updating scene items for ${tableName} ${id}: ${currentItem.address} -> ${address}`
        );
        updateSceneItemsAddress(db, id, tableName, address);
      }
    }

    return getProjectItemById(db, id, tableName);
  } catch (error) {
    console.error(`Failed to update ${tableName} item:`, error);
    throw error;
  }
}

// Helper method to update scene items when item addresses change
export function updateSceneItemsAddress(db, itemId, itemType, newAddress) {
  try {
    const stmt = db.prepare(`
      UPDATE scene_items
      SET item_address = ?
      WHERE item_id = ? AND item_type = ?
    `);

    const result = stmt.run(newAddress, itemId, itemType);
    console.log(
      `Updated ${result.changes} scene items with new address ${newAddress} for ${itemType} item ${itemId}`
    );

    return result.changes;
  } catch (error) {
    console.error("Failed to update scene items address:", error);
    throw error;
  }
}
