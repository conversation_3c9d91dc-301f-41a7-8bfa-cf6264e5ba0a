import { getProjectItems, getProjectItemById, deleteProjectItem, duplicateProjectItem } from "./generic-operations.js";
import { createProjectItem } from "./lighting-operations.js";
import { updateProjectItem } from "./update-operations.js";

/**
 * Multi-scene operations
 */
export function getMultiSceneItems(db, projectId) {
  return getProjectItems(db, projectId, "multi_scenes");
}

export function createMultiSceneItem(db, projectId, itemData) {
  return createProjectItem(db, projectId, itemData, "multi_scenes");
}

export function updateMultiSceneItem(db, id, itemData) {
  return updateProjectItem(db, id, itemData, "multi_scenes");
}

export function deleteMultiSceneItem(db, id) {
  return deleteProjectItem(db, id, "multi_scenes");
}

export function duplicateMultiSceneItem(db, id) {
  return duplicateProjectItem(db, id, "multi_scenes", createProjectItem, getProjectItemById);
}

// Multi-scene scene operations
export function getMultiSceneScenes(db, multiSceneId) {
  try {
    const stmt = db.prepare(`
      SELECT s.id, s.name, s.address, s.description, mss.scene_order
      FROM scene s
      INNER JOIN multi_scene_scenes mss ON s.id = mss.scene_id
      WHERE mss.multi_scene_id = ?
      ORDER BY mss.scene_order ASC
    `);
    return stmt.all(multiSceneId);
  } catch (error) {
    console.error("Failed to get multi-scene scenes:", error);
    throw error;
  }
}

export function updateMultiSceneScenes(db, multiSceneId, sceneIds) {
  try {
    const transaction = db.transaction(() => {
      // First, delete existing associations
      const deleteStmt = db.prepare("DELETE FROM multi_scene_scenes WHERE multi_scene_id = ?");
      deleteStmt.run(multiSceneId);

      // Then add new associations with order
      if (sceneIds && sceneIds.length > 0) {
        const insertStmt = db.prepare(`
          INSERT INTO multi_scene_scenes (multi_scene_id, scene_id, scene_order)
          VALUES (?, ?, ?)
        `);

        sceneIds.forEach((sceneId, index) => {
          insertStmt.run(multiSceneId, sceneId, index);
        });
      }

      return getMultiSceneScenes(db, multiSceneId);
    });

    return transaction();
  } catch (error) {
    console.error("Failed to update multi-scene scenes:", error);
    throw error;
  }
}

export function deleteMultiSceneScenes(db, multiSceneId) {
  try {
    const stmt = db.prepare("DELETE FROM multi_scene_scenes WHERE multi_scene_id = ?");
    const result = stmt.run(multiSceneId);
    return { success: true, deletedCount: result.changes };
  } catch (error) {
    console.error("Failed to delete multi-scene scenes:", error);
    throw error;
  }
}

// Get multi-scene item with scenes
export function getMultiSceneItemById(db, id) {
  try {
    const multiScene = getProjectItemById(db, id, "multi_scenes");
    if (!multiScene) {
      return null;
    }

    // Get associated scenes
    multiScene.scenes = getMultiSceneScenes(db, id);

    return multiScene;
  } catch (error) {
    console.error("Failed to get multi-scene item by id:", error);
    throw error;
  }
}

// Get all multi-scene items with scenes
export function getMultiSceneItemsWithScenes(db, projectId) {
  try {
    const multiScenes = getMultiSceneItems(db, projectId);

    return multiScenes.map((multiScene) => {
      // Get associated scenes
      multiScene.scenes = getMultiSceneScenes(db, multiScene.id);
      return multiScene;
    });
  } catch (error) {
    console.error("Failed to get multi-scene items with scenes:", error);
    throw error;
  }
}
