import { getProjectItems, getProjectItemById, deleteProjectItem, duplicateProjectItem } from "./generic-operations.js";
import { createProjectItem } from "./lighting-operations.js";
import { updateProjectItem } from "./update-operations.js";
import { getObjectValue } from "./utils.js";

/**
 * Scene operations
 */
export function getSceneItems(db, projectId) {
  return getProjectItems(db, projectId, "scene");
}

export function createSceneItem(db, projectId, itemData) {
  return createProjectItem(db, projectId, itemData, "scene");
}

export function updateSceneItem(db, id, itemData) {
  return updateProjectItem(db, id, itemData, "scene");
}

export function deleteSceneItem(db, id) {
  return deleteProjectItem(db, id, "scene");
}

export function duplicateSceneItem(db, id) {
  return duplicateProjectItem(db, id, "scene", createProjectItem, getProjectItemById);
}

// Scene item operations
export function getSceneItemsBySceneId(db, sceneId) {
  try {
    const stmt = db.prepare("SELECT * FROM scene_items WHERE scene_id = ?");
    return stmt.all(sceneId);
  } catch (error) {
    console.error("Failed to get scene items:", error);
    throw error;
  }
}

export function createSceneItemBatch(db, sceneId, items) {
  try {
    const transaction = db.transaction(() => {
      // First, delete existing scene items
      const deleteStmt = db.prepare("DELETE FROM scene_items WHERE scene_id = ?");
      deleteStmt.run(sceneId);

      // Then insert new scene items
      const insertStmt = db.prepare(`
        INSERT INTO scene_items (scene_id, item_type, item_id, item_address, item_value, command, object_type, object_value)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const createdItems = [];
      items.forEach((item) => {
        const object_value = getObjectValue(item.object_type);
        const result = insertStmt.run(
          sceneId,
          item.item_type,
          item.item_id,
          item.item_address,
          item.item_value,
          item.command,
          item.object_type,
          object_value
        );
        createdItems.push({
          id: result.lastInsertRowid,
          scene_id: sceneId,
          ...item,
          object_value,
        });
      });

      return createdItems;
    });

    return transaction();
  } catch (error) {
    console.error("Failed to create scene item batch:", error);
    throw error;
  }
}

export function deleteSceneItemsBySceneId(db, sceneId) {
  try {
    const stmt = db.prepare("DELETE FROM scene_items WHERE scene_id = ?");
    const result = stmt.run(sceneId);
    return { success: true, deletedCount: result.changes };
  } catch (error) {
    console.error("Failed to delete scene items:", error);
    throw error;
  }
}

// Scene address items operations
export function getSceneAddressItems(db, projectId) {
  try {
    const stmt = db.prepare(
      "SELECT * FROM scene_address_items WHERE project_id = ? ORDER BY address ASC"
    );
    return stmt.all(projectId);
  } catch (error) {
    console.error("Failed to get scene address items:", error);
    throw error;
  }
}

export function createSceneAddressItem(db, projectId, itemData) {
  try {
    const { address, item_type, item_id, object_type } = itemData;
    const object_value = getObjectValue(object_type);

    const stmt = db.prepare(`
      INSERT OR IGNORE INTO scene_address_items (project_id, address, item_type, item_id, object_type, object_value)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      projectId,
      address,
      item_type,
      item_id,
      object_type,
      object_value
    );

    if (result.changes > 0) {
      return {
        id: result.lastInsertRowid,
        project_id: projectId,
        address,
        item_type,
        item_id,
        object_type,
        object_value,
      };
    }

    // If no changes, item already exists, return existing item
    const existingStmt = db.prepare(
      "SELECT * FROM scene_address_items WHERE project_id = ? AND address = ? AND item_type = ? AND item_id = ? AND object_type = ?"
    );
    return existingStmt.get(projectId, address, item_type, item_id, object_type);
  } catch (error) {
    console.error("Failed to create scene address item:", error);
    throw error;
  }
}

export function deleteSceneAddressItem(db, projectId, address, itemType, itemId, objectType) {
  try {
    const stmt = db.prepare(
      "DELETE FROM scene_address_items WHERE project_id = ? AND address = ? AND item_type = ? AND item_id = ? AND object_type = ?"
    );
    const result = stmt.run(projectId, address, itemType, itemId, objectType);
    return { success: true, deletedCount: result.changes };
  } catch (error) {
    console.error("Failed to delete scene address item:", error);
    throw error;
  }
}

export function deleteSceneAddressItemsByItemId(db, itemType, itemId) {
  try {
    const stmt = db.prepare(
      "DELETE FROM scene_address_items WHERE item_type = ? AND item_id = ?"
    );
    const result = stmt.run(itemType, itemId);
    return { success: true, deletedCount: result.changes };
  } catch (error) {
    console.error("Failed to delete scene address items by item id:", error);
    throw error;
  }
}
