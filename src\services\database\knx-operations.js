import { getProjectItems, getProjectItemById, deleteProjectItem, duplicateProjectItem } from "./generic-operations.js";
import { createProjectItem } from "./lighting-operations.js";
import { updateProjectItem } from "./update-operations.js";

/**
 * KNX operations
 */
export function getKnxItems(db, projectId) {
  return getProjectItems(db, projectId, "knx");
}

export function createKnxItem(db, projectId, itemData) {
  return createProjectItem(db, projectId, itemData, "knx");
}

export function updateKnxItem(db, id, itemData) {
  return updateProjectItem(db, id, itemData, "knx");
}

export function deleteKnxItem(db, id) {
  return deleteProjectItem(db, id, "knx");
}

export function duplicateKnxItem(db, id) {
  return duplicateProjectItem(db, id, "knx", createProjectItem, getProjectItemById);
}
