import { getObjectValue, findNextAvailableAddress } from "./utils.js";

/**
 * Generic CRUD operations for project items
 */
export function getProjectItems(db, projectId, tableName) {
  try {
    // Sort by address ASC for tables with address field, except unit and schedule tables
    if (tableName === "unit") {
      const stmt = db.prepare(
        `SELECT * FROM ${tableName} WHERE project_id = ? ORDER BY created_at DESC`
      );
      const items = stmt.all(projectId);
      // Parse RS485 and I/O config from JSON for unit items
      return items.map((item) => ({
        ...item,
        rs485_config: item.rs485_config
          ? JSON.parse(item.rs485_config)
          : null,
        input_configs: item.input_configs ? JSON.parse(item.input_configs) : null,
        output_configs: item.output_configs ? JSON.parse(item.output_configs) : null,
      }));
    } else if (tableName === "schedule" || tableName === "multi_scenes") {
      const stmt = db.prepare(
        `SELECT * FROM ${tableName} WHERE project_id = ? ORDER BY name ASC`
      );
      return stmt.all(projectId);
    } else {
      const stmt = db.prepare(
        `SELECT * FROM ${tableName} WHERE project_id = ? ORDER BY address ASC`
      );
      return stmt.all(projectId);
    }
  } catch (error) {
    throw error;
  }
}

export function getProjectItemById(db, id, tableName) {
  try {
    const stmt = db.prepare(`SELECT * FROM ${tableName} WHERE id = ?`);
    const item = stmt.get(id);

    // Parse RS485 and I/O config from JSON for unit items
    if (tableName === "unit" && item) {
      if (item.rs485_config) {
        item.rs485_config = JSON.parse(item.rs485_config);
      }
      if (item.input_configs) {
        item.input_configs = JSON.parse(item.input_configs);
      }
      if (item.output_configs) {
        item.output_configs = JSON.parse(item.output_configs);
      }
    }

    return item;
  } catch (error) {
    throw error;
  }
}

export function deleteProjectItem(db, id, tableName) {
  try {
    const stmt = db.prepare(`DELETE FROM ${tableName} WHERE id = ?`);
    const result = stmt.run(id);

    if (result.changes === 0) {
      throw new Error(`${tableName} item not found`);
    }

    return { success: true, deletedId: id };
  } catch (error) {
    console.error(`Failed to delete ${tableName} item:`, error);
    throw error;
  }
}

export function duplicateProjectItem(db, id, tableName, createProjectItemFn, getProjectItemByIdFn) {
  try {
    const originalItem = getProjectItemByIdFn(db, id, tableName);

    if (!originalItem) {
      throw new Error(`${tableName} item not found`);
    }

    const duplicatedItem = {
      name: originalItem.name ? `${originalItem.name} (Copy)` : null,
      address: originalItem.address,
      description: originalItem.description,
    };

    // Add object_type only for tables that use it (not aircon, knx, scene, multi_scenes)
    if (
      tableName !== "aircon" &&
      tableName !== "knx" &&
      tableName !== "scene" &&
      tableName !== "multi_scenes"
    ) {
      duplicatedItem.object_type = originalItem.object_type;
    }

    // For lighting, find a unique address in range 1-255
    if (tableName === "lighting" && originalItem.address) {
      duplicatedItem.address = findNextAvailableAddress(
        db,
        originalItem.project_id,
        "lighting"
      );
    }

    // For aircon, include label and find unique address in range 1-255
    if (tableName === "aircon") {
      duplicatedItem.label = originalItem.label;

      if (originalItem.address) {
        duplicatedItem.address = findNextAvailableAddress(
          db,
          originalItem.project_id,
          "aircon"
        );
      }
    }

    // For curtain, find a unique address in range 1-255 and include curtain-specific fields
    if (tableName === "curtain" && originalItem.address) {
      duplicatedItem.address = findNextAvailableAddress(
        db,
        originalItem.project_id,
        "curtain"
      );
      duplicatedItem.curtain_type =
        originalItem.curtain_type || "CURTAIN_PULSE_2P";
      duplicatedItem.curtain_value = originalItem.curtain_value || 3;
      duplicatedItem.open_group_id = originalItem.open_group_id || null;
      duplicatedItem.close_group_id = originalItem.close_group_id || null;
      duplicatedItem.stop_group_id = originalItem.stop_group_id || null;
      duplicatedItem.pause_period = originalItem.pause_period || 0;
      duplicatedItem.transition_period = originalItem.transition_period || 0;
    }

    // For scene, find a unique address in range 1-255 if address exists
    if (tableName === "scene" && originalItem.address) {
      duplicatedItem.address = findNextAvailableAddress(
        db,
        originalItem.project_id,
        "scene"
      );
    }

    // For KNX, find a unique address if address exists
    if (
      tableName === "knx" &&
      originalItem.address !== null &&
      originalItem.address !== undefined
    ) {
      // For KNX, find next available address in range 0-511
      let newAddress = originalItem.address;
      do {
        newAddress = (newAddress + 1) % 512;
      } while (
        db
          .prepare(
            "SELECT COUNT(*) as count FROM knx WHERE project_id = ? AND address = ?"
          )
          .get(originalItem.project_id, newAddress).count > 0 &&
        newAddress !== originalItem.address
      );

      duplicatedItem.address = newAddress;

      // Copy KNX-specific fields
      duplicatedItem.type = originalItem.type || 0;
      duplicatedItem.factor = originalItem.factor || 2;
      duplicatedItem.feedback = originalItem.feedback || 0;
      duplicatedItem.rcu_group_id = originalItem.rcu_group_id || null;
      duplicatedItem.knx_switch_group = originalItem.knx_switch_group || null;
      duplicatedItem.knx_dimming_group =
        originalItem.knx_dimming_group || null;
      duplicatedItem.knx_value_group = originalItem.knx_value_group || null;
    }

    // For multi_scenes, copy type and address fields
    if (tableName === "multi_scenes") {
      duplicatedItem.type = originalItem.type || 0;
      duplicatedItem.address = originalItem.address || "";
    }

    return createProjectItemFn(
      originalItem.project_id,
      duplicatedItem,
      tableName
    );
  } catch (error) {
    console.error(`Failed to duplicate ${tableName} item:`, error);
    throw error;
  }
}

// Get all project items in one optimized call
export function getAllProjectItems(db, projectId) {
  try {
    const lighting = db
      .prepare(
        "SELECT * FROM lighting WHERE project_id = ? ORDER BY address ASC"
      )
      .all(projectId);
    const aircon = db
      .prepare(
        "SELECT * FROM aircon WHERE project_id = ? ORDER BY address ASC"
      )
      .all(projectId);
    const unitRaw = db
      .prepare(
        "SELECT * FROM unit WHERE project_id = ? ORDER BY created_at DESC"
      )
      .all(projectId);

    // Parse RS485 and I/O config from JSON for unit items
    const unit = unitRaw.map((item) => ({
      ...item,
      rs485_config: item.rs485_config
        ? JSON.parse(item.rs485_config)
        : null,
      input_configs: item.input_configs ? JSON.parse(item.input_configs) : null,
      output_configs: item.output_configs ? JSON.parse(item.output_configs) : null,
    }));
    const curtain = db
      .prepare(
        "SELECT * FROM curtain WHERE project_id = ? ORDER BY address ASC"
      )
      .all(projectId);
    const knx = db
      .prepare("SELECT * FROM knx WHERE project_id = ? ORDER BY address ASC")
      .all(projectId);
    const scene = db
      .prepare(
        "SELECT * FROM scene WHERE project_id = ? ORDER BY address ASC"
      )
      .all(projectId);
    const schedule = db
      .prepare(
        "SELECT * FROM schedule WHERE project_id = ? ORDER BY name ASC"
      )
      .all(projectId);
    const multi_scenes = db
      .prepare(
        "SELECT * FROM multi_scenes WHERE project_id = ? ORDER BY name ASC"
      )
      .all(projectId);
    const sequences = db
      .prepare(
        "SELECT * FROM sequences WHERE project_id = ? ORDER BY name ASC"
      )
      .all(projectId);

    return {
      lighting,
      aircon,
      unit,
      curtain,
      knx,
      scene,
      schedule,
      multi_scenes,
      sequences,
    };
  } catch (error) {
    console.error("Failed to get all project items:", error);
    throw error;
  }
}
