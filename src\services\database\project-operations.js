import { getAllProjectItems } from "./generic-operations.js";

/**
 * Project CRUD operations
 */
export function getAllProjects(db) {
  try {
    const stmt = db.prepare(
      "SELECT * FROM projects ORDER BY created_at DESC"
    );
    return stmt.all();
  } catch (error) {
    console.error("Failed to get all projects:", error);
    throw error;
  }
}

export function getProjectById(db, id) {
  try {
    const stmt = db.prepare("SELECT * FROM projects WHERE id = ?");
    return stmt.get(id);
  } catch (error) {
    console.error("Failed to get project by id:", error);
    throw error;
  }
}

export function createProject(db, projectData) {
  try {
    const { name, description } = projectData;

    const stmt = db.prepare(`
      INSERT INTO projects (name, description)
      VALUES (?, ?)
    `);

    const result = stmt.run(name, description);
    return getProjectById(db, result.lastInsertRowid);
  } catch (error) {
    console.error("Failed to create project:", error);
    throw error;
  }
}

export function updateProject(db, id, projectData) {
  try {
    const { name, description } = projectData;

    const stmt = db.prepare(`
      UPDATE projects
      SET name = ?, description = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    const result = stmt.run(name, description, id);

    if (result.changes === 0) {
      throw new Error("Project not found");
    }

    return getProjectById(db, id);
  } catch (error) {
    console.error("Failed to update project:", error);
    throw error;
  }
}

export function deleteProject(db, id) {
  try {
    const stmt = db.prepare("DELETE FROM projects WHERE id = ?");
    const result = stmt.run(id);

    if (result.changes === 0) {
      throw new Error("Project not found");
    }

    return { success: true, deletedId: id };
  } catch (error) {
    console.error("Failed to delete project:", error);
    throw error;
  }
}

export function duplicateProject(db, id, createProjectFn, copyProjectItemsFn) {
  try {
    const originalProject = getProjectById(db, id);

    if (!originalProject) {
      throw new Error("Project not found");
    }

    // Start transaction for atomic operation
    const transaction = db.transaction(() => {
      // Create the new project
      const duplicatedProject = {
        name: `${originalProject.name} (Copy)`,
        description: originalProject.description,
      };

      const newProject = createProjectFn(duplicatedProject);

      // Get all items from the original project
      const originalItems = getAllProjectItems(db, id);

      // Copy all items to the new project
      copyProjectItemsFn(originalItems, newProject.id);

      return newProject;
    });

    return transaction();
  } catch (error) {
    console.error("Failed to duplicate project:", error);
    throw error;
  }
}

// Import project with all items
export function importProject(db, projectData, itemsData, createProjectFn, createUnitItemFn, createProjectItemFn, createScheduleItemFn) {
  try {
    // Start transaction
    const transaction = db.transaction(() => {
      // Create project
      const project = createProjectFn(projectData);

      // Import items for each category
      const categories = [
        "lighting",
        "aircon",
        "unit",
        "curtain",
        "knx",
        "scene",
        "schedule",
      ];
      const importedCounts = {};

      categories.forEach((category) => {
        const items = itemsData[category] || [];
        importedCounts[category] = 0;

        items.forEach((itemData) => {
          if (category === "unit") {
            createUnitItemFn(project.id, itemData);
          } else if (category === "aircon") {
            // Ensure label is set for aircon items
            if (!itemData.label) {
              itemData.label = "Aircon";
            }
            createProjectItemFn(project.id, itemData, "aircon");
          } else if (category === "schedule") {
            createScheduleItemFn(project.id, itemData);
          } else {
            createProjectItemFn(project.id, itemData, category);
          }
          importedCounts[category]++;
        });
      });

      return { project, importedCounts };
    });

    return transaction();
  } catch (error) {
    console.error("Failed to import project:", error);
    throw error;
  }
}

// Bulk import items for a specific category
export function bulkImportItems(db, projectId, items, category, createUnitItemFn, createProjectItemFn, createScheduleItemFn) {
  try {
    const transaction = db.transaction(() => {
      const importedItems = [];

      items.forEach((itemData) => {
        let item;
        if (category === "unit") {
          item = createUnitItemFn(projectId, itemData);
        } else if (category === "aircon") {
          item = createProjectItemFn(projectId, itemData, "aircon");
        } else if (category === "schedule") {
          item = createScheduleItemFn(projectId, itemData);
        } else {
          item = createProjectItemFn(projectId, itemData, category);
        }
        importedItems.push(item);
      });

      return importedItems;
    });

    return transaction();
  } catch (error) {
    console.error(`Failed to bulk import ${category} items:`, error);
    throw error;
  }
}
